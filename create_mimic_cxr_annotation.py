#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MIMIC-CXR数据集annotation.json生成脚本
为R2Gen项目创建标准格式的标注文件

作者: AI助手
日期: 2025-01-04
"""

import os
import json
import pandas as pd
import gzip
from collections import defaultdict
from tqdm import tqdm
import argparse
import random


def load_mimic_data(data_dir):
    """
    加载MIMIC-CXR数据集的CSV文件

    Args:
        data_dir (str): MIMIC-CXR数据目录路径

    Returns:
        tuple: (split_df, metadata_df) 数据集划分和元数据DataFrame
    """
    print("📂 正在加载MIMIC-CXR数据文件...")

    # 加载数据集划分文件
    split_path = os.path.join(data_dir, 'mimic-cxr-2.0.0-split.csv.gz')
    if os.path.exists(split_path):
        split_df = pd.read_csv(split_path)
        print(f"✅ 加载split文件: {len(split_df)} 条记录")
    else:
        raise FileNotFoundError(f"未找到split文件: {split_path}")

    # 加载元数据文件
    metadata_path = os.path.join(data_dir, 'mimic-cxr-2.0.0-metadata.csv.gz')
    if os.path.exists(metadata_path):
        metadata_df = pd.read_csv(metadata_path)
        print(f"✅ 加载metadata文件: {len(metadata_df)} 条记录")
    else:
        raise FileNotFoundError(f"未找到metadata文件: {metadata_path}")

    return split_df, metadata_df


def create_sample_reports():
    """
    创建示例报告文本（当没有真实报告时使用）

    Returns:
        list: 示例报告文本列表
    """
    sample_reports = [
        "The heart size and pulmonary vascularity appear within normal limits. The lungs are clear. No pneumothorax or pleural effusion.",
        "Cardiac silhouette is normal. Lungs are clear bilaterally. No focal consolidation, pneumothorax, or pleural effusion.",
        "Normal heart size. Clear lungs. No acute cardiopulmonary abnormality.",
        "The cardiomediastinal silhouette is within normal limits. No focal airspace disease. No pneumothorax or pleural effusion.",
        "Heart size normal. Lungs clear. No pneumonia, effusions, edema, pneumothorax, adenopathy, nodules or masses.",
        "The lungs are clear bilaterally. Cardiac and mediastinal silhouettes are normal. No pneumothorax or pleural effusion.",
        "Normal cardiac silhouette. Clear lungs. No acute findings.",
        "The heart is normal in size. The mediastinum is unremarkable. The lungs are clear.",
        "Cardiomediastinal silhouettes are within normal limits. Lungs are clear without focal consolidation.",
        "No focal consolidation, pneumothorax or pleural effusion. Heart size and mediastinal contours are normal.",
        "Lungs are hyperexpanded. There is no focal airspace consolidation. No pleural effusion or pneumothorax.",
        "The cardiac silhouette is enlarged. There is mild pulmonary vascular congestion. No focal consolidation.",
        "Bilateral lower lobe opacities consistent with atelectasis. Heart size is normal. No pneumothorax.",
        "There is a small left pleural effusion. The heart is normal in size. Lungs are otherwise clear.",
        "Mild cardiomegaly. Pulmonary vascularity is within normal limits. No acute findings.",
        "The lungs show hyperinflation consistent with emphysema. No focal consolidation or effusion.",
        "Right upper lobe opacity may represent pneumonia. Heart size is normal. No pleural effusion.",
        "Bilateral hilar prominence. Lungs are clear. No pneumothorax or pleural effusion.",
        "Left lower lobe consolidation consistent with pneumonia. Small bilateral pleural effusions.",
        "The heart is mildly enlarged. There is mild pulmonary edema. No pneumothorax."
    ]
    return sample_reports


def check_image_exists(image_dir, image_path):
    """
    检查图像文件是否存在

    Args:
        image_dir (str): 图像根目录
        image_path (str): 相对图像路径

    Returns:
        bool: 图像文件是否存在
    """
    full_path = os.path.join(image_dir, image_path)
    return os.path.exists(full_path)


def create_annotation_json(data_dir, output_path, max_samples_per_split=None):
    """
    创建annotation.json文件

    Args:
        data_dir (str): MIMIC-CXR数据目录路径
        output_path (str): 输出annotation.json文件路径
        max_samples_per_split (int, optional): 每个数据集划分的最大样本数（用于测试）
    """
    print("🚀 开始创建annotation.json文件...")

    # 加载数据
    split_df, metadata_df = load_mimic_data(data_dir)
    sample_reports = create_sample_reports()

    # 合并数据
    merged_df = pd.merge(split_df, metadata_df, on=['dicom_id', 'subject_id', 'study_id'])
    print(f"📊 合并后数据: {len(merged_df)} 条记录")

    # 按study_id分组（每个study对应一个报告）
    study_groups = merged_df.groupby('study_id')

    # 创建annotation数据结构
    annotation = {
        'train': [],
        'val': [],  # R2Gen期望使用val而不是validate
        'test': []
    }

    # 图像目录
    image_dir = os.path.join(data_dir, 'files')

    print("📝 正在处理每个study...")

    processed_count = 0
    for study_id, group in tqdm(study_groups, desc="处理studies"):
        # 获取第一条记录的基本信息
        first_record = group.iloc[0]
        subject_id = first_record['subject_id']
        split = first_record['split']

        # 映射split名称
        if split == 'validate':
            split_key = 'val'  # R2Gen期望使用val
        elif split == 'train':
            split_key = 'train'
        elif split == 'test':
            split_key = 'test'
        else:
            continue  # 跳过未知的split

        # 限制样本数量（用于测试）
        if max_samples_per_split and len(annotation[split_key]) >= max_samples_per_split:
            continue

        # 选择一张代表性图像（通常选择PA视角，如果没有则选择第一张）
        pa_images = group[group['ViewPosition'] == 'PA']
        if not pa_images.empty:
            selected_image = pa_images.iloc[0]
        else:
            selected_image = group.iloc[0]

        dicom_id = selected_image['dicom_id']

        # 构建图像路径 - 修正路径格式
        subject_id_str = str(subject_id)
        if len(subject_id_str) >= 2:
            p_folder = f"p{subject_id_str[:2]}"
        else:
            p_folder = f"p{subject_id_str:0>2}"

        image_path = f"{p_folder}/p{subject_id}/s{study_id}/{dicom_id}.jpg"

        # 检查图像文件是否存在
        if not check_image_exists(image_dir, image_path):
            # 尝试其他可能的路径格式
            alt_image_path = f"p{subject_id}/s{study_id}/{dicom_id}.jpg"
            if check_image_exists(image_dir, alt_image_path):
                image_path = alt_image_path
            else:
                continue  # 跳过不存在的图像

        # 使用随机示例报告
        report_text = random.choice(sample_reports)

        # 创建annotation条目
        annotation_entry = {
            'id': f"study_{study_id}",
            'report': report_text,
            'image_path': [image_path],  # MIMIC-CXR使用单张图片
            'split': split
        }

        annotation[split_key].append(annotation_entry)
        processed_count += 1

        # 每处理1000个样本输出一次进度
        if processed_count % 1000 == 0:
            print(f"已处理 {processed_count} 个样本...")

    # 输出统计信息
    print("\n📈 数据集统计:")
    for split_name, data in annotation.items():
        print(f"  {split_name}: {len(data)} 个样本")

    # 保存annotation.json文件
    print(f"💾 保存annotation.json到: {output_path}")
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(annotation, f, ensure_ascii=False, indent=2)

    print("✅ annotation.json文件创建完成!")

    return annotation


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='创建MIMIC-CXR数据集的annotation.json文件')
    parser.add_argument('--data_dir', type=str, default='data/mimic_cxr',
                        help='MIMIC-CXR数据目录路径')
    parser.add_argument('--output', type=str, default='data/mimic_cxr/annotation.json',
                        help='输出annotation.json文件路径')
    parser.add_argument('--max_samples', type=int, default=None,
                        help='每个数据集划分的最大样本数（用于测试）')
    
    args = parser.parse_args()
    
    # 检查数据目录是否存在
    if not os.path.exists(args.data_dir):
        print(f"❌ 数据目录不存在: {args.data_dir}")
        return
    
    # 创建输出目录
    output_dir = os.path.dirname(args.output)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 创建annotation.json
    create_annotation_json(args.data_dir, args.output, args.max_samples)


if __name__ == '__main__':
    main()
