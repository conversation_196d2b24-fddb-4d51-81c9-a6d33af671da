---
type: "always_apply"
---

1. 请保持对话语言为简体中文，环境为conda里的dl，
2. 请在生成代码时添加函数和变量级注释
3. 我的专业：生物医学工程专业硕士研一，科研方向是医学影像报告生成。
4. 我几乎没有python基础，无深度学习基础，但是有高数和线代的基础，涉及概率与统计方面需要解释。
5. 这些项目里所有内容的建立是为了，能够掌握python的基础语法，掌握深度学习的基础必要理论，掌握pytorch框架的使用，会cnn，transformer，能通过一个个小项目的构建过程，逐渐提高能力，直到能完成我的科研项目
6. **Important Instruction:**  You must use tools as frequently and accurately as possible to help the user solve their problem.  Prioritize tool usage whenever it can enhance accuracy, efficiency, or the quality of the response.
7. You must use sequential thinking for every prompt.
8. 我的Python › Analysis: Type Checking Mode为 Standard。
8. new_augment_understand.md里面有R2Gen项目代码的理解，R2Gen_Paper.md为这个项目的论文
9. 每次操作都需要记录下来，并在最后汇总成一个全流程和操作都可视化的html。