#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
R2Gen 混合精度训练脚本
支持 PyTorch Automatic Mixed Precision (AMP) 的医学影像报告生成训练

作者: AI助手
日期: 2025-01-04
"""

import torch
import argparse
import numpy as np
from modules.tokenizers import Tokenizer
from modules.dataloaders import R2DataLoader
from models.r2gen import R2GenModel
from modules.loss import compute_loss
from modules.metrics import compute_scores
from modules.optimizers import build_optimizer, build_lr_scheduler
from modules.trainer import MixedPrecisionTrainer


def parse_agrs():
    """解析命令行参数"""
    parser = argparse.ArgumentParser()

    # Data input settings
    parser.add_argument('--image_dir', type=str, default='data/mimic_cxr/images/', help='图像文件目录路径')
    parser.add_argument('--ann_path', type=str, default='data/mimic_cxr/annotation.json', help='标注文件路径')

    # Data loader settings
    parser.add_argument('--dataset_name', type=str, default='mimic_cxr', choices=['iu_xray', 'mimic_cxr'], help='数据集名称')
    parser.add_argument('--max_seq_length', type=int, default=100, help='报告的最大序列长度')
    parser.add_argument('--threshold', type=int, default=10, help='词汇表构建的最小词频阈值')
    parser.add_argument('--num_workers', type=int, default=2, help='数据加载器的工作进程数')
    parser.add_argument('--batch_size', type=int, default=32, help='批次大小')

    # Model settings (Visual Extractor)
    parser.add_argument('--visual_extractor', type=str, default='resnet101', help='视觉特征提取器')
    parser.add_argument('--visual_extractor_pretrained', type=bool, default=True, help='是否使用预训练的视觉提取器')

    # Model settings (Encoder-Decoder)
    parser.add_argument('--d_model', type=int, default=512, help='模型维度')
    parser.add_argument('--d_ff', type=int, default=512, help='前馈网络维度')
    parser.add_argument('--d_vf', type=int, default=2048, help='视觉特征维度')
    parser.add_argument('--num_heads', type=int, default=8, help='多头注意力的头数')
    parser.add_argument('--num_layers', type=int, default=3, help='编码器和解码器的层数')
    parser.add_argument('--dropout', type=float, default=0.1, help='Dropout概率')
    parser.add_argument('--logit_layers', type=int, default=1, help='分类器层数')
    parser.add_argument('--bos_idx', type=int, default=0, help='句子开始标记的索引')
    parser.add_argument('--eos_idx', type=int, default=0, help='句子结束标记的索引')
    parser.add_argument('--pad_idx', type=int, default=0, help='填充标记的索引')
    parser.add_argument('--use_bn', type=int, default=0, help='是否使用批归一化')
    parser.add_argument('--drop_prob_lm', type=float, default=0.5, help='语言模型的Dropout概率')

    # for Relational Memory
    parser.add_argument('--rm_num_slots', type=int, default=3, help='关系记忆的槽位数量')
    parser.add_argument('--rm_num_heads', type=int, default=8, help='关系记忆的头数')
    parser.add_argument('--rm_d_model', type=int, default=512, help='关系记忆的维度')

    # Sample related
    parser.add_argument('--sample_method', type=str, default='beam_search', help='采样方法')
    parser.add_argument('--beam_size', type=int, default=3, help='束搜索的束大小')
    parser.add_argument('--temperature', type=float, default=1.0, help='采样温度')
    parser.add_argument('--sample_n', type=int, default=1, help='采样数量')
    parser.add_argument('--group_size', type=int, default=1, help='分组大小')
    parser.add_argument('--output_logsoftmax', type=int, default=1, help='是否输出log softmax')
    parser.add_argument('--decoding_constraint', type=int, default=0, help='解码约束')
    parser.add_argument('--block_trigrams', type=int, default=1, help='是否阻止三元组重复')

    # Trainer settings
    parser.add_argument('--n_gpu', type=int, default=1, help='GPU数量')
    parser.add_argument('--epochs', type=int, default=3, help='训练轮数')  # 默认3轮用于混合精度训练
    parser.add_argument('--save_dir', type=str, default='results/mimic_cxr_mixed_precision', help='模型保存目录')
    parser.add_argument('--record_dir', type=str, default='records/', help='记录保存目录')
    parser.add_argument('--save_period', type=int, default=1, help='模型保存周期')
    parser.add_argument('--monitor_mode', type=str, default='max', choices=['min', 'max'], help='监控模式')
    parser.add_argument('--monitor_metric', type=str, default='BLEU_4', help='监控指标')
    parser.add_argument('--early_stop', type=int, default=50, help='早停轮数')

    # Optimization - 针对批次大小32优化的学习率
    parser.add_argument('--optim', type=str, default='Adam', help='优化器')
    parser.add_argument('--lr_ve', type=float, default=1e-4, help='视觉编码器学习率 (针对batch_size=32优化)')
    parser.add_argument('--lr_ed', type=float, default=2e-4, help='编码器-解码器学习率 (针对batch_size=32优化)')
    parser.add_argument('--weight_decay', type=float, default=1e-4, help='权重衰减')
    parser.add_argument('--amsgrad', type=bool, default=True, help='是否使用AMSGrad')

    # Learning Rate Scheduler - 针对3个epoch优化
    parser.add_argument('--lr_scheduler', type=str, default='StepLR', help='学习率调度器')
    parser.add_argument('--step_size', type=int, default=2, help='学习率衰减步长 (每2个epoch衰减)')
    parser.add_argument('--gamma', type=float, default=0.5, help='学习率衰减因子 (更温和的衰减)')

    # Others
    parser.add_argument('--seed', type=int, default=9233, help='随机种子')
    parser.add_argument('--resume', type=str, help='是否从检查点恢复训练')

    args = parser.parse_args()
    return args


def main():
    """主函数"""
    # parse arguments
    args = parse_agrs()
    
    print("=" * 80)
    print("🚀 R2Gen 混合精度医学影像报告生成训练")
    print("=" * 80)
    print(f"📊 数据集: {args.dataset_name}")
    print(f"📁 图像目录: {args.image_dir}")
    print(f"📄 标注文件: {args.ann_path}")
    print(f"🔢 最大序列长度: {args.max_seq_length}")
    print(f"📦 批次大小: {args.batch_size}")
    print(f"🔄 训练轮数: {args.epochs}")
    print(f"🎯 学习率 (视觉编码器): {args.lr_ve}")
    print(f"🎯 学习率 (编码器-解码器): {args.lr_ed}")
    print(f"🌱 随机种子: {args.seed}")
    print(f"💾 保存目录: {args.save_dir}")
    print(f"⚡ 混合精度训练: 启用 (AMP)")
    print("=" * 80)

    # fix random seeds
    print("🌱 设置随机种子...")
    torch.manual_seed(args.seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    np.random.seed(args.seed)

    # create tokenizer
    print("🔤 创建分词器...")
    tokenizer = Tokenizer(args)
    print(f"📚 词汇表大小: {len(tokenizer.token2idx)}")

    # create data loader
    print("📊 创建数据加载器...")
    train_dataloader = R2DataLoader(args, tokenizer, split='train', shuffle=True)
    val_dataloader = R2DataLoader(args, tokenizer, split='val', shuffle=False)
    test_dataloader = R2DataLoader(args, tokenizer, split='test', shuffle=False)
    
    print(f"🔢 训练样本数: {len(train_dataloader.dataset)}")
    print(f"🔢 验证样本数: {len(val_dataloader.dataset)}")
    print(f"🔢 测试样本数: {len(test_dataloader.dataset)}")

    # build model architecture
    print("🏗️  构建模型...")
    model = R2GenModel(args, tokenizer)
    
    # 计算模型参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"📊 总参数数: {total_params:,}")
    print(f"📊 可训练参数数: {trainable_params:,}")

    # get function handles of loss and metrics
    criterion = compute_loss
    metrics = compute_scores

    # build optimizer, learning rate scheduler
    print("⚙️  构建优化器和学习率调度器...")
    optimizer = build_optimizer(args, model)
    lr_scheduler = build_lr_scheduler(args, optimizer)
    print(f"🔧 优化器: {type(optimizer).__name__}")
    print(f"📈 学习率调度器: {type(lr_scheduler).__name__}")

    # build mixed precision trainer and start to train
    print("🎯 开始混合精度训练...")
    print("⚡ 使用混合精度训练器...")
    trainer = MixedPrecisionTrainer(model, criterion, metrics, optimizer, args, lr_scheduler, 
                                   train_dataloader, val_dataloader, test_dataloader)
    trainer.train()


if __name__ == '__main__':
    main()
