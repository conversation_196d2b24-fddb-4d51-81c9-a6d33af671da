# 🚀 R2Gen 医学影像报告生成训练指南

## 📋 目录
- [项目概述](#项目概述)
- [环境要求](#环境要求)
- [数据准备](#数据准备)
- [训练配置](#训练配置)
- [训练执行](#训练执行)
- [结果分析](#结果分析)
- [故障排除](#故障排除)

## 🎯 项目概述

R2Gen 是一个基于 Transformer 的医学影像报告生成模型，支持从胸部 X 光片自动生成医学报告。本项目实现了两种训练模式：

- **标准训练**: 传统的全精度训练
- **混合精度训练**: 使用 PyTorch AMP 的高效训练

### 🏥 支持的数据集
- **MIMIC-CXR**: 大规模胸部X光数据集 (推荐)
- **IU X-Ray**: 印第安纳大学胸部X光数据集

## 🔧 环境要求

### 硬件要求
- **GPU**: NVIDIA GPU (推荐 RTX 3080 或更高)
- **显存**: 至少 8GB (混合精度训练), 16GB+ (标准训练)
- **内存**: 至少 16GB RAM
- **存储**: 至少 100GB 可用空间

### 软件环境
```bash
# Python 环境
Python 3.8+
PyTorch 1.9+
CUDA 11.0+

# 主要依赖
torch>=1.9.0
torchvision>=0.10.0
tqdm
pandas
numpy
```

### Conda 环境设置
```bash
# 创建环境
conda create -n dl python=3.12
conda activate dl

# 安装 PyTorch (根据你的CUDA版本调整)
conda install pytorch torchvision torchaudio pytorch-cuda=12.1 -c pytorch -c nvidia

# 安装其他依赖
pip install tqdm pandas numpy
```

## 📊 数据准备

### 1. MIMIC-CXR 数据集结构
```
data/mimic_cxr/
├── files/                          # 图像文件
│   ├── p10/p10000032/s50414267/    # 按患者和研究组织
│   └── ...
├── mimic-cxr-2.0.0-split.csv.gz    # 数据集划分
├── mimic-cxr-2.0.0-metadata.csv.gz # 图像元数据
├── images/                          # 软链接到files目录
└── annotation.json                  # 生成的标注文件
```

### 2. 生成 annotation.json
```bash
# 生成完整数据集的标注文件
python create_mimic_cxr_annotation.py \
    --data_dir data/mimic_cxr \
    --output data/mimic_cxr/annotation.json

# 生成测试用的小数据集 (可选)
python create_mimic_cxr_annotation.py \
    --data_dir data/mimic_cxr \
    --output data/mimic_cxr/annotation_test.json \
    --max_samples 1000
```

### 3. 创建图像目录软链接
```bash
cd data/mimic_cxr
ln -sf files images
```

## ⚙️ 训练配置

### 关键参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--batch_size` | 32 | 批次大小，根据GPU显存调整 |
| `--epochs` | 3 | 训练轮数 |
| `--lr_ve` | 1e-4 | 视觉编码器学习率 |
| `--lr_ed` | 2e-4 | 编码器-解码器学习率 |
| `--max_seq_length` | 100 | 报告最大长度 |
| `--step_size` | 2 | 学习率衰减步长 |
| `--gamma` | 0.5 | 学习率衰减因子 |

### 批次大小与显存关系

| 批次大小 | 显存需求 | 训练速度 | 推荐GPU |
|----------|----------|----------|---------|
| 8 | ~6GB | 慢 | GTX 1080 Ti |
| 16 | ~10GB | 中等 | RTX 3080 |
| 32 | ~16GB | 快 | RTX 3090/4090 |
| 64 | ~28GB | 很快 | A100 |

## 🚀 训练执行

### 1. 混合精度训练 (推荐)
```bash
# 完整训练 (3个epoch)
python main_train_mixed_precision.py \
    --image_dir data/mimic_cxr/images/ \
    --ann_path data/mimic_cxr/annotation.json \
    --dataset_name mimic_cxr \
    --max_seq_length 100 \
    --threshold 10 \
    --batch_size 32 \
    --epochs 3 \
    --save_dir results/mimic_cxr_mixed_precision \
    --seed 456789

# 快速测试 (1个epoch, 小批次)
python main_train_mixed_precision.py \
    --batch_size 8 \
    --epochs 1 \
    --save_dir results/test_run
```

### 2. 标准训练
```bash
python main_train.py \
    --image_dir data/mimic_cxr/images/ \
    --ann_path data/mimic_cxr/annotation.json \
    --dataset_name mimic_cxr \
    --mixed_precision \
    --batch_size 16 \
    --epochs 3
```

### 3. 训练过程监控

训练过程中会显示：
- 📊 实时训练进度条
- 📈 Loss 变化趋势
- ⚡ 混合精度缩放因子
- 🎯 验证指标 (BLEU, ROUGE等)
- ⏰ 训练时间统计

## 📈 结果分析

### 1. 模型文件
```
results/mimic_cxr_mixed_precision/
├── model_best.pth          # 最佳模型
├── current_checkpoint.pth  # 当前检查点
└── training_log.txt        # 训练日志
```

### 2. 评估指标

| 指标 | 说明 | 目标值 |
|------|------|--------|
| BLEU-4 | 文本相似度 | >0.15 |
| ROUGE-L | 最长公共子序列 | >0.35 |
| CIDEr | 共识评估 | >0.8 |
| Loss | 训练损失 | <2.0 |

### 3. 训练曲线分析
- **Loss 下降**: 应该平稳下降，无剧烈波动
- **验证指标**: 应该逐步提升
- **过拟合检测**: 验证loss开始上升时停止训练

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 显存不足 (CUDA out of memory)
```bash
# 解决方案：
# 1. 减小批次大小
--batch_size 16  # 或更小

# 2. 减少序列长度
--max_seq_length 80

# 3. 使用梯度累积
--gradient_accumulation_steps 2
```

#### 2. 数据加载错误
```bash
# 检查数据路径
ls data/mimic_cxr/images/
ls data/mimic_cxr/annotation.json

# 重新生成annotation.json
python create_mimic_cxr_annotation.py --data_dir data/mimic_cxr --output data/mimic_cxr/annotation.json
```

#### 3. 混合精度数值溢出
```bash
# 已修复：将 -1e9 改为 -1e4
# 如果仍有问题，可以禁用混合精度
python main_train.py --mixed_precision False
```

#### 4. 训练速度慢
```bash
# 优化建议：
# 1. 增加数据加载器工作进程
--num_workers 4

# 2. 使用更大的批次大小
--batch_size 32

# 3. 启用混合精度训练
python main_train_mixed_precision.py
```

### 性能优化建议

#### 1. 硬件优化
- 使用 NVMe SSD 存储数据
- 确保充足的系统内存
- 使用多GPU训练 (如果可用)

#### 2. 软件优化
- 启用 CUDA 优化
- 使用最新版本的 PyTorch
- 合理设置数据加载器参数

#### 3. 训练策略
- 使用学习率预热
- 实施梯度裁剪
- 定期保存检查点

## 📚 进阶使用

### 1. 自定义数据集
```python
# 修改 create_mimic_cxr_annotation.py
# 适配你的数据格式
```

### 2. 模型微调
```bash
# 从预训练模型开始
--resume results/mimic_cxr_mixed_precision/model_best.pth
```

### 3. 超参数调优
```bash
# 使用不同的学习率
--lr_ve 5e-5 --lr_ed 1e-4

# 调整模型结构
--num_layers 6 --num_heads 12
```

## 🎓 学习资源

### 相关论文
- R2Gen: "Generating Radiology Reports via Memory-driven Transformer"
- Transformer: "Attention Is All You Need"
- Mixed Precision: "Mixed Precision Training"

### 代码参考
- 原始 R2Gen 实现
- PyTorch 官方 AMP 教程
- Hugging Face Transformers

---

## 📞 支持与反馈

如果遇到问题或有改进建议，请：
1. 检查本指南的故障排除部分
2. 查看训练日志文件
3. 记录详细的错误信息

**祝你训练顺利！** 🚀
