Microsoft COCO Caption Evaluation Tools <br />
---

Modified the code to work with Python 3. <br />

### Requirements
* Python 3.x
* Java 1.8
* pycocotools

---

### Tested on
* Windows 10, Python 3.5.

---
### To fix Windows JVM memory error: <br />
Add the following in System Variables <br />
&nbsp;&nbsp;&nbsp;&nbsp;Variable name : _JAVA_OPTIONS <br />
&nbsp;&nbsp;&nbsp;&nbsp;Variable value : -Xmx1024M <br />

---
Original code : https://github.com/tylin/coco-caption <br />
